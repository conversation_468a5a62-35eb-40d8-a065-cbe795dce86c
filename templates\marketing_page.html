    <!-- base template -->

    {% include "baseTemplate.html" %}

    <!-- Sidebar -->
     
    {% include "sidebar.html" %}
    
    <br>
    <div class="d-flex justify-content-center">
        <div class="alert alert-success text-center" role="alert" style="width: 20rem;">
           DASHBOARD / MARKETING
        </div>
    </div>

    <!-- Dashboard content -->
    <div class="container-fluid">
        
        <div class="row">
            <div class="col-md-12">
                <div class="card marketing_page_card">
                    <div class="card-header">
                        <h4 class="card-title">Student Data</h4>
                    </div>
                        {% if messages %}
                            {% for message in messages %}
                            <div class="alert alert-primary alert-dismissible fade show" role="alert">
                                {{ message }}<br>
                            </div>
                            {% endfor %}
                        {% endif %}
                    <div class="card-body">
                        <!-- <input type="text" id="search" class="form-control my-3" placeholder="Search students..."> -->
                        <div class="d-flex align-items-center gap-3 flex-md-nowrap flex-wrap rounded" style="max-width: 100%;">
                                    <select id="courseDropdown" class="form-control" style="margin: 0.2rem !important" onchange="applyFilters()">
                                        <option value="">Course Name</option>
                                    </select>
                                    <select id="nameDropdown" class="form-control" style="margin: 0.2rem !important" onchange="applyFilters()">
                                        <option value="">Name</option>
                                    </select>
                                    <select id="genderDropdown" class="form-control" style="margin: 0.2rem !important" onchange="applyFilters()">
                                        <option value="">Gender</option>
                                    </select>
                                    <select id="phoneDropdown" class="form-control" style="margin: 0.2rem !important" onchange="applyFilters()">
                                        <option value="">Phone</option>
                                    </select>
                                    <select id="emailDropdown" class="form-control" style="margin: 0.2rem !important" onchange="applyFilters()">
                                        <option value="">Email</option>
                                    </select>
                                    <!-- dueDropdown -->
                                    <input type="date" id="startDate" class="form-control" style="margin: 0.2rem !important" onchange="applyFilters()" />
                                    <!-- <br> -->
                                    <input type="date" id="endDate" class="form-control" style="margin: 0.2rem !important" onchange="applyFilters()" />
                        </div>
                        <div class="table-responsive">
                            <form method="POST" action="/librarian/process-student-data/">
                                {% csrf_token %}
                                <!-- Table -->
                                <div class="mt-3">
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>
                                                    <div style="display: flex; justify-content: center; align-items: center; gap: 10px">
                                                        <b></b>
                                                        <input type="checkbox" onclick="checkAll()" style="width: 20px; height: 20px" />
                                                    </div>
                                                </th>
                                                <th>Course Name <i class="fa-solid fa-arrow-up-a-z" onclick="sortTable(1)"></i></th>
                                                <th>Name <i class="fa-solid fa-arrow-up-a-z" onclick="sortTable(2)"></i></th>
                                                <th>Gender <i class="fa-solid fa-arrow-up-a-z" onclick="sortTable(3)"></i></th>
                                                <th>Phone <i class="fa-solid fa-arrow-up-a-z" onclick="sortTable(4)"></i></th>
                                                <th>Email <i class="fa-solid fa-arrow-up-a-z" onclick="sortTable(5)"></i></th>
                                                <th>Due Date <i class="fa-solid fa-arrow-up-a-z" onclick="sortTable(6, true)"></i></th>
                                            </tr>
                                        </thead>
                                        <!-- <tfoot>
                                            <tr>
                                                <th></th>
                                                <th>
                                                    <select id="courseDropdown" onchange="applyFilters()">
                                                        <option value="">Course Name</option>
                                                    </select>
                                                </th>
                                                <th>
                                                    <select id="nameDropdown" onchange="applyFilters()">
                                                        <option value="">Name</option>
                                                    </select>
                                                </th>
                                                <th>
                                                    <select id="genderDropdown" onchange="applyFilters()">
                                                        <option value="">Gender</option>
                                                    </select>
                                                </th>
                                                <th>
                                                    <select id="phoneDropdown" onchange="applyFilters()">
                                                        <option value="">Phone</option>
                                                    </select>
                                                </th>
                                                <th>
                                                    <select id="emailDropdown" onchange="applyFilters()">
                                                        <option value="">Email</option>
                                                    </select>
                                                </th>
                                                <th>
                                                    <input type="date" id="startDate" class="m-1" onchange="applyFilters()" />
                                                    <br>
                                                    <input type="date" id="endDate" class="m-1" onchange="applyFilters()" />
                                                </th>
                                            </tr>
                                        </tfoot> -->
                                        <tbody id="studentTableBody">
                                            {% for data in student_data %}
                                            <tr class="student-row"
                                                data-course-name="{{ data.student.course }}"
                                                data-name="{{ data.student.name }}"
                                                data-gender="{{ data.student.gender }}"
                                                data-phone="{{ data.student.mobile }}"
                                                data-email="{{ data.student.email }}"
                                                data-due-date="{{ data.invoice_data.due_date }}">
                                                <td><input type="checkbox" name="student_checkbox" value="{{ data.student.slug }}" onclick="checkThis(event)" style="width: 20px; height: 20px; margin-left: 0.5rem !important;" /></td>
                                                <td>{{ data.student.course }}</td>
                                                <td class="student-name">{{ data.student.name }}</td>
                                                <td>{{ data.student.gender }}</td>
                                                <td>{{ data.student.mobile }}</td>
                                                <td>{{ data.student.email }}</td>
                                                <td>{{ data.invoice_data.due_date }}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                <div class="row justify-content-center mt-3">
                                    <div class="col-md-3 text-center">
                                        <button type="submit" id="proceedButton" class="proceed btn btn-primary w-100 mb-2 disabled">Proceed</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <nav class="mt-2 d-flex justify-content-center">
                            <ul class="pagination" id="pagination">
                                <!-- Pagination buttons will be inserted here -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>


        <!-- <script>
            document.addEventListener("DOMContentLoaded", function () {
                const rowsPerPage = 5;
                const tableBody = document.getElementById("studentTableBody");
                const pagination = document.getElementById("pagination");
                const rows = document.querySelectorAll(".student-row");
                const searchInput = document.getElementById("search");
                let currentPage = 1;
                let filteredRows = rows;
            
                function displayTable(page) {
                    let start = (page - 1) * rowsPerPage;
                    let end = start + rowsPerPage;
                    filteredRows.forEach((row, index) => {
                        row.style.display = index >= start && index < end ? "" : "none";
                    });
                }
            
                function setupPagination() {
                    let pageCount = Math.ceil(filteredRows.length / rowsPerPage);
                    pagination.innerHTML = "";
            
                    for (let i = 1; i <= pageCount; i++) {
                        let li = document.createElement("li");
                        li.className = `page-item ${i === currentPage ? "active" : ""}`;
                        let link = document.createElement("a");
                        link.className = "page-link";
                        link.href = "#";
                        link.innerText = i;
                        link.addEventListener("click", function (e) {
                            e.preventDefault();
                            currentPage = i;
                            displayTable(currentPage);
                            setupPagination();
                        });
                        li.appendChild(link);
                        pagination.appendChild(li);
                    }
                }
            
                function searchStudents() {
                    const query = searchInput.value.toLowerCase();
                    filteredRows = rows.filter(row => 
                        row.querySelector(".student-name").textContent.toLowerCase().includes(query)
                    );
                    currentPage = 1; 
                    displayTable(currentPage);
                    setupPagination();
                }

                searchInput.addEventListener("input", searchStudents);

                displayTable(currentPage);
                setupPagination();
            });
        </script> -->


        <!-- pagiantion new logic -->
        <script>
            document.addEventListener("DOMContentLoaded", function () {
    const rowsPerPage = 5;
    const tableBody = document.getElementById("studentTableBody");
    const pagination = document.getElementById("pagination");
    const rows = document.querySelectorAll(".student-row");
    const searchInput = document.getElementById("search");
    let currentPage = 1;
    let filteredRows = rows;
    
    // Function to display the table based on current page
    function displayTable(page) {
        let start = (page - 1) * rowsPerPage;
        let end = start + rowsPerPage;
        filteredRows.forEach((row, index) => {
            row.style.display = index >= start && index < end ? "" : "none";
        });
    }
    
    // Function to set up pagination buttons
    function setupPagination() {
        let pageCount = Math.ceil(filteredRows.length / rowsPerPage);
        pagination.innerHTML = "";

        // Add Previous Button
        let prevPage = document.createElement("li");
        prevPage.className = "page-item" + (currentPage === 1 ? " disabled" : "");
        let prevLink = document.createElement("a");
        prevLink.className = "page-link";
        prevLink.href = "#";
        prevLink.innerText = "Previous";
        prevLink.addEventListener("click", function (e) {
            e.preventDefault();
            if (currentPage > 1) {
                currentPage--;
                displayTable(currentPage);
                setupPagination();
            }
        });
        prevPage.appendChild(prevLink);
        pagination.appendChild(prevPage);

        // Calculate which page numbers to show (show 4 pages + last page)
        let startPage = Math.max(1, currentPage - 2); // Show pages before current page
        let endPage = Math.min(pageCount, currentPage + 2); // Show pages after current page

        if (startPage > 1) {
            let firstPage = document.createElement("li");
            firstPage.className = "page-item";
            let firstLink = document.createElement("a");
            firstLink.className = "page-link";
            firstLink.href = "#";
            firstLink.innerText = "1";
            firstLink.addEventListener("click", function (e) {
                e.preventDefault();
                currentPage = 1;
                displayTable(currentPage);
                setupPagination();
            });
            firstPage.appendChild(firstLink);
            pagination.appendChild(firstPage);

            let ellipsis = document.createElement("li");
            ellipsis.className = "page-item disabled";
            let ellipsisLink = document.createElement("span");
            ellipsisLink.className = "page-link";
            ellipsisLink.innerText = "...";
            ellipsis.appendChild(ellipsisLink);
            pagination.appendChild(ellipsis);
        }

        // Add the page number buttons
        for (let i = startPage; i <= endPage; i++) {
            let pageItem = document.createElement("li");
            pageItem.className = "page-item" + (i === currentPage ? " active" : "");
            let pageLink = document.createElement("a");
            pageLink.className = "page-link";
            pageLink.href = "#";
            pageLink.innerText = i;
            pageLink.addEventListener("click", function (e) {
                e.preventDefault();
                currentPage = i;
                displayTable(currentPage);
                setupPagination();
            });
            pageItem.appendChild(pageLink);
            pagination.appendChild(pageItem);
        }

        // Add Ellipsis and Last Page Button
        if (endPage < pageCount) {
            let ellipsis = document.createElement("li");
            ellipsis.className = "page-item disabled";
            let ellipsisLink = document.createElement("span");
            ellipsisLink.className = "page-link";
            ellipsisLink.innerText = "...";
            ellipsis.appendChild(ellipsisLink);
            pagination.appendChild(ellipsis);

            let lastPage = document.createElement("li");
            lastPage.className = "page-item";
            let lastLink = document.createElement("a");
            lastLink.className = "page-link";
            lastLink.href = "#";
            lastLink.innerText = pageCount;
            lastLink.addEventListener("click", function (e) {
                e.preventDefault();
                currentPage = pageCount;
                displayTable(currentPage);
                setupPagination();
            });
            lastPage.appendChild(lastLink);
            pagination.appendChild(lastPage);
        }

        // Add Next Button
        let nextPage = document.createElement("li");
        nextPage.className = "page-item" + (currentPage === pageCount ? " disabled" : "");
        let nextLink = document.createElement("a");
        nextLink.className = "page-link";
        nextLink.href = "#";
        nextLink.innerText = "Next";
        nextLink.addEventListener("click", function (e) {
            e.preventDefault();
            if (currentPage < pageCount) {
                currentPage++;
                displayTable(currentPage);
                setupPagination();
            }
        });
        nextPage.appendChild(nextLink);
        pagination.appendChild(nextPage);
    }
    
    // Search function for filtering students
    function searchStudents() {
        const query = searchInput.value.toLowerCase();
        filteredRows = Array.from(rows).filter(row => 
            row.querySelector(".student-name").textContent.toLowerCase().includes(query)
          
        );
        console.log("Filtered Row", filteredRows)
        currentPage = 1; // Reset to the first page
        displayTable(currentPage);
        setupPagination();
    }

    // Apply the search event
    searchInput.addEventListener("input", searchStudents);

    // Initialize the table and pagination
    displayTable(currentPage);
    setupPagination();
});

        </script>

    <!-- Check Box in table -->
    <script>

        // This is the data in which selected row will come (to pass)
        let selectedRowsData = []; // Array to store data of selected rows
        let allChecked = false; // Toggle state for checkAll

    
        function checkAll() {
            allChecked = !allChecked; // Toggle state
            selectedRowsData = []; // Clear the array for fresh selection
    
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            
            checkboxes.forEach((checkbox) => {
                checkbox.checked = allChecked; // Set all checkboxes based on toggle state
                const row = checkbox.closest('tr'); // Get the row of this checkbox
                const rowData = Array.from(row.children).slice(1).reduce((acc, cell, index) => {
                    const headers = ['Course Name', 'Name', 'Gender', 'Phone', 'Email', 'Due Date']; // Define column headers
                    acc[headers[index]] = cell.innerText; // Map headers to cell values
                    return acc;
                }, {});
    
                if (allChecked) {
                    selectedRowsData.push(rowData); // Add data if checked
                }
            });
    
            console.log("Selected rows data:", selectedRowsData);
        }
    
        function checkThis(event) {
            const checkbox = event.target;
            const row = checkbox.closest('tr');
            const rowData = Array.from(row.children).slice(1).reduce((acc, cell, index) => {
                const headers = ['Course Name', 'Name', 'Gender', 'Phone', 'Email', 'Due Date'];
                acc[headers[index]] = cell.innerText;
                return acc;
            }, {});
    
            if (checkbox.checked) {
                selectedRowsData.push(rowData); // Add to selected rows if checked
            } else {
                // Remove row from selected rows if unchecked
                selectedRowsData = selectedRowsData.filter(item => item['Name'] !== rowData['Name']);
            }
    
            updateButtonVisibility();
            console.log("Selected rows data:", selectedRowsData);
        }
        
        function updateButtonVisibility() {
            const button = document.getElementById('proceedButton');
            if (selectedRowsData.length > 0) {
                button.classList.remove('disabled'); // Enable button
            } else {
                button.classList.add('disabled'); // Disable button
            }
        }
        
    </script>

    <!-- Sorting of body -->
    <script>
        let sortOrder = {}; // To keep track of the sorting order for each column

        function sortTable(columnIndex, isDate = false) {
            const table = document.querySelector("table tbody");
            const rows = Array.from(table.rows);
            
            // Toggle sorting order
            sortOrder[columnIndex] = !sortOrder[columnIndex];
            
            rows.sort((rowA, rowB) => {
                let cellA = rowA.cells[columnIndex].textContent;
                let cellB = rowB.cells[columnIndex].textContent;

                if (isDate) {
                    cellA = new Date(cellA);
                    cellB = new Date(cellB);
                } else if (!isNaN(cellA) && !isNaN(cellB)) {
                    // If values are numbers, parse them
                    cellA = parseFloat(cellA);
                    cellB = parseFloat(cellB);
                }

                // Determine the sort order (ascending or descending)
                if (sortOrder[columnIndex]) {
                    return cellA > cellB ? 1 : -1;
                } else {
                    return cellA < cellB ? 1 : -1;
                }
            });

            // Append the sorted rows back to the table
            rows.forEach(row => table.appendChild(row));
        }
    </script>

    <!-- Filter dropdown -->
    <script>
        // Helper function to parse dates in 'DD-MM-YYYY' format
        function parseDate(dateString) {
            const parts = dateString.split('-');
            // Return new Date in 'YYYY-MM-DD' format
            return new Date(`${parts[2]}-${parts[1]}-${parts[0]}`); 
        }

        function applyFilters() {
            const courseDropdown = document.getElementById("courseDropdown").value;
            const nameDropdown = document.getElementById("nameDropdown").value;
            const genderDropdown = document.getElementById("genderDropdown").value;
            const phoneDropdown = document.getElementById("phoneDropdown").value;
            const emailDropdown = document.getElementById("emailDropdown").value;
            const startDateInput = document.getElementById("startDate").value;
            const endDateInput = document.getElementById("endDate").value;

            // Parse start and end dates
            const startDate = startDateInput ? new Date(startDateInput) : null;
            const endDate = endDateInput ? new Date(endDateInput) : null;

            const rows = document.querySelectorAll("tbody tr");

            rows.forEach(row => {
                const course = row.getAttribute("data-course-name");
                const name = row.getAttribute("data-name");
                const gender = row.getAttribute("data-gender");
                const phone = row.getAttribute("data-phone");
                const email = row.getAttribute("data-email");
                const dueDate = parseDate(row.getAttribute("data-due-date")); // Parse using the helper function

                // Filter conditions
                const courseMatch = courseDropdown === "" || courseDropdown === course;
                const nameMatch = nameDropdown === "" || nameDropdown === name;
                const genderMatch = genderDropdown === "" || genderDropdown === gender;
                const phoneMatch = phoneDropdown === "" || phoneDropdown === phone;
                const emailMatch = emailDropdown === "" || emailDropdown === email;

                // Date range match
                const dateMatch = 
                    (startDate === null || dueDate >= startDate) && 
                    (endDate === null || dueDate <= endDate);

                // Display rows that match all conditions
                row.style.display = (courseMatch && nameMatch && genderMatch && phoneMatch && emailMatch && dateMatch) ? "" : "none";
            });
        }

    </script>

    <!-- Foot of table -->
    <script>
        document.addEventListener("DOMContentLoaded", () => {
            populateDropdowns();
        });
        
        function populateDropdowns() {
            const rows = document.querySelectorAll("tbody tr");
            const uniqueValues = {
                course: new Set(),
                name: new Set(),
                gender: new Set(),
                phone: new Set(),
                email: new Set()
            };
        
            // Collect unique values from each row
            rows.forEach(row => {
                uniqueValues.course.add(row.cells[1].textContent.trim());
                uniqueValues.name.add(row.cells[2].textContent.trim());
                uniqueValues.gender.add(row.cells[3].textContent.trim());
                uniqueValues.phone.add(row.cells[4].textContent.trim());
                uniqueValues.email.add(row.cells[5].textContent.trim());
            });
        
            // Populate each dropdown with unique values
            addOptionsToDropdown(uniqueValues.course, "courseDropdown");
            addOptionsToDropdown(uniqueValues.name, "nameDropdown");
            addOptionsToDropdown(uniqueValues.gender, "genderDropdown");
            addOptionsToDropdown(uniqueValues.phone, "phoneDropdown");
            addOptionsToDropdown(uniqueValues.email, "emailDropdown");
        }
        
        function addOptionsToDropdown(valuesSet, dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            valuesSet.forEach(value => {
                const option = document.createElement("option");
                option.value = option.textContent = value;
                dropdown.appendChild(option);
            });
        }
    </script>    


        <!-- footer -->
        <div class="footer">
            <img src="/static/img/librainian-logo-white-trans.png" alt="Librainian Logo" loading="lazy" style="filter: invert(0.5) brightness(0);">            <!-- <p>Developed with passion by Librainian</p> -->
        </div>
    </div>


    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <!-- <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script> -->
    <!-- <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script> -->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    
</body>
</html>