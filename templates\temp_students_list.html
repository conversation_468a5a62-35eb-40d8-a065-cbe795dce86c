    <!-- base template -->

    {% include "baseTemplate.html" %}

    <!-- Sidebar -->
     
    {% include "sidebar.html" %}

    <br/>

    <div class="container-fluid py-4">
        <div class="card" style="background: #fff !important;">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="mb-0 fs-1 text-start">Pending Students </h3>
            </a>
            </div>
            
            <div class="card-body">
                {% if messages %}
                    <div class="alert alert-success alert-dismissible fade show fs-6" role="alert">
                        {% for message in messages %}
                            {{ message }}
                        {% endfor %}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endif %}
    
                <!-- Desktop Table View -->
                <div class="table-responsive d-none d-md-block">
                    <table class="table table-hover table-striped align-middle">
                        <thead class="table-dark">
                            <tr>
                                <th class="fs-6">Name</th>
                                <th class="fs-6">Email</th>
                                <th class="fs-6">Mobile</th>
                                <th class="fs-6">State</th>
                                <th class="fs-6">Course</th>
                                <th class="fs-6">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in temp_students %}
                                <tr>
                                    <td class="fs-6">{{ student.name }}</td>
                                    <td class="fs-6">{{ student.email }}</td>
                                    <td class="fs-6">{{ student.mobile }}</td>
                                    <td class="fs-6">{{ student.state.name }}</td>
                                    <td class="fs-6">{{ student.course.name }}</td>
                                    <td>
                                        <span class="badge p-2 rounded-2 text-white {% if student.status == 'Active' %}bg-success{% else %}bg-secondary{% endif %}">
                                            {{ student.status }}
                                        </span>
                                    </td>
                                    <td>
                                        <form method="POST" class="d-inline">
                                            {% csrf_token %}
                                            <input type="hidden" name="student_id" value="{{ student.id }}">
                                            <button 
                                                type="submit" 
                                                name="status" 
                                                value="completed" 
                                                class="btn btn-primary btn-sm"
                                                {% if student.status == 'Completed' %}disabled{% endif %}
                                            >
                                                Complete
                                            </button>
                                        </form>
                                    </td>
                                    <td>
                                        <a href="{% url 'delete_temp_student' student.id %}" onclick="return confirm('Are you sure?')">Delete</a>

                                    </td>
                                    <td>
                                        <a href="{% url 'update_temp_student' student.id %}">Edit</a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
    
                <!-- Mobile Card View -->
                <div class="d-block d-md-none">
                    {% for student in temp_students %}
                        <div class="card mb-3 shadow-sm">
                            <div class="card-body">
                                <h5 class="card-title fs-5">{{ student.name }}</h5>
                                <p class="mb-1 fs-6"><strong>Email:</strong> {{ student.email }}</p>
                                <p class="mb-1 fs-6"><strong>Mobile:</strong> {{ student.mobile }}</p>
                                <p class="mb-1 fs-6"><strong>State:</strong> {{ student.state.name }}</p>
                                <p class="mb-1 fs-6"><strong>Course:</strong> {{ student.course.name }}</p>
                                <p>
                                    <strong>Status:</strong> 
                                    <span class="badge {% if student.status == 'Active' %}bg-success{% else %}bg-secondary{% endif %}">
                                        {{ student.status }}
                                    </span>
                                </p>
                                <form method="POST" class="mt-2">
                                    {% csrf_token %}
                                    <input type="hidden" name="student_id" value="{{ student.id }}">
                                    <button 
                                        type="submit" 
                                        name="status" 
                                        value="completed" 
                                        class="btn btn-primary btn-sm"
                                        {% if student.status == 'Completed' %}disabled{% endif %}
                                    >
                                        Complete
                                    </button>
                                </form>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    
    

    <!-- Add Student Modal -->
    <div class="modal fade" id="addStudentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Student</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="mb-3">
                            <label class="form-label">Full Name</label>
                            <input type="text" class="form-control" required>
                        </div>
                        <!-- <div class="mb-3">
                            <label class="form-label">Father's Name</label>
                            <input type="text" class="form-control" required>
                        </div> -->
                        <!-- Additional form fields would go here -->
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary">Save Student</button>
                </div>
            </div>
        </div>
    </div>

<!-- jQuery (necessary for Bootstrap's JavaScript plugins) -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- Popper.js (necessary for Bootstrap's dropdowns and tooltips) -->
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>

<!-- Bootstrap 4 JavaScript -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

</body>
</html>