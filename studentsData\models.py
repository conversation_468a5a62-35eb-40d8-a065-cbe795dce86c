from django.db import models
from django.utils import timezone
from django.utils.text import slugify
from librarian.models import Librarian_param
from subLibrarian.models import Sublibrarian_param
from datetime import datetime
from django.core.exceptions import ValidationError
import random
import string


class Timing(models.Model):
    start_time = models.TimeField()
    end_time = models.TimeField()

    def __str__(self):
        return f"{self.start_time} - {self.end_time}"


class Shift(models.Model):
    librarian = models.ForeignKey(
        Librarian_param, on_delete=models.CASCADE, null=True, blank=True
    )
    name = models.CharField(max_length=100)
    time_range = models.CharField(max_length=100)
    price = models.IntegerField(default=0)

    def __str__(self):
        return self.name


class Months(models.Model):
    name = models.CharField(max_length=100, unique=True)

    def __str__(self):
        return self.name


class States(models.Model):
    name = models.Char<PERSON>ield(max_length=100, unique=True)

    def __str__(self):
        return self.name


class Courses(models.Model):
    name = models.CharField(max_length=100, unique=True)

    def __str__(self):
        return self.name


class StudentData(models.Model):
    librarian = models.ForeignKey(
        Librarian_param, on_delete=models.CASCADE, null=True, blank=True
    )
    sublibrarian = models.ForeignKey(
        Sublibrarian_param, on_delete=models.CASCADE, null=True, blank=True
    )
    unique_id = models.CharField(max_length=20, unique=True, blank=True)
    course = models.ForeignKey(Courses, on_delete=models.CASCADE)
    name = models.CharField(max_length=100)
    f_name = models.CharField(max_length=100, null=True, blank=True, default=None)
    age = models.IntegerField(null=True, blank=True, default=0)
    gender = models.CharField(max_length=100)
    email = models.EmailField(max_length=100)
    mobile = models.BigIntegerField()
    locality = models.CharField(max_length=100)
    city = models.CharField(max_length=100)
    state = models.ForeignKey(States, on_delete=models.CASCADE)
    registration_date = models.DateField(default=timezone.now)
    registration_fee = models.IntegerField()
    # is_first_registration = models.BooleanField(default=True)
    image = models.ImageField(upload_to="student_image/", blank=True, null=True)
    slug = models.SlugField(max_length=250, blank=True)
    color = models.CharField(max_length=20, default="normal")

    class Meta:
        unique_together = ("librarian", "email", "mobile")

    def __str__(self):
        return f"{self.name} | {self.unique_id}"

    def generate_unique_id(self):
        if self.librarian:
            library_prefix = self.librarian.user.username[:4].lower()
        elif self.sublibrarian:
            library_prefix = self.sublibrarian.librarian.user.username[:4].lower()
        else:
            raise ValueError("Either librarian or sublibrarian must be set")

        today = timezone.now()
        date_str = today.strftime("%m%y")
        count = (
            StudentData.objects.filter(
                unique_id__startswith=f"{library_prefix}_{date_str}"
            ).count()
            + 1
        )

        if count > 999:
            raise ValidationError(
                "The maximum number of registrations for this month has been reached."
            )

        unique_id = f"{library_prefix}_{date_str}_{count:03d}"

        # Retry mechanism to ensure unique_id is unique
        while StudentData.objects.filter(unique_id=unique_id).exists():
            count += 1
            if count > 999:
                raise ValidationError(
                    "The maximum number of registrations for this month has been reached."
                )
            unique_id = f"{library_prefix}_{date_str}_{count:03d}"

        return unique_id

    # def set_due_color(self):
    #     today = timezone.now().date()
    #     latest_invoice = self.invoice_set.order_by("-due_date").first()  # type: ignore

    #     if latest_invoice and latest_invoice.due_date:
    #         if isinstance(latest_invoice.due_date, str):
    #             try:
    #                 due_date = datetime.strptime(
    #                     latest_invoice.due_date, "%Y-%m-%d"
    #                 ).date()
    #             except ValueError:
    #                 due_date = today  # Fallback if date parsing fails
    #         else:
    #             due_date = latest_invoice.due_date

    #         delta = (due_date - today).days

    #         if delta <= -10:
    #             self.color = "grey"
    #         elif delta >= 1 and delta <= 6:
    #             self.color = "blue"
    #         elif delta == 0:
    #             self.color = "green"
    #         elif delta <= -1 and delta >= -9:
    #             self.color = "red"
    #         else:
    #             self.color = "normal"
    #     else:
    #         self.color = "normal"

    def save(self, *args, **kwargs):
        if not self.unique_id:
            self.unique_id = self.generate_unique_id()

        if self.sublibrarian and not self.librarian:
            self.librarian = self.sublibrarian.librarian

        if not self.librarian:
            raise ValueError("Either librarian or sublibrarian must be set")

        if not self.slug:
            base_slug = slugify(self.name + " " + self.city)
            unique_slug = base_slug
            num = 1
            while StudentData.objects.filter(slug=unique_slug).exists():
                unique_slug = f"{base_slug}-{num}"
                num += 1
            self.slug = unique_slug

        super().save(*args, **kwargs)


class Invoice(models.Model):
    invoice_id = models.CharField(max_length=20, unique=True, blank=True)
    student = models.ForeignKey(StudentData, on_delete=models.CASCADE)
    shift = models.ManyToManyField(Shift)
    months = models.ManyToManyField(Months)
    issue_date = models.DateField(default=timezone.now)
    due_date = models.DateField()
    discount_amount = models.IntegerField(default=0)
    total_amount = models.IntegerField()
    mode_pay = models.CharField(max_length=100, default="Cash")
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    slug = models.SlugField(max_length=250, blank=True)

    def __str__(self):
        return f"Invoice: {self.invoice_id} - {self.student.name}"

    def generate_invoice_id(self):
        today = timezone.now()
        date_str = today.strftime("%m%y")

        # Get the active user's first name
        active_user = self.student.librarian
        first_name = active_user.user.first_name.replace(" ", "_")  # type: ignore

        # Initialize count
        count = 1

        while True:
            invoice_id = f"inv_{first_name}_{date_str}_{count:03d}"
            if not Invoice.objects.filter(invoice_id=invoice_id).exists():
                return invoice_id
            count += 1

    def save(self, *args, **kwargs):
        if not self.invoice_id:
            self.invoice_id = self.generate_invoice_id()

        if not self.slug:
            self.slug = slugify(self.invoice_id)

        super().save(*args, **kwargs)


class RegistrationFee(models.Model):
    student = models.OneToOneField(StudentData, on_delete=models.CASCADE)
    reg_unique_id = models.CharField(max_length=30, unique=True, blank=True)
    is_paid = models.BooleanField(default=False)
    slug = models.SlugField(max_length=1000, blank=True)

    def __str__(self):
        return f"Registration Fee: {self.reg_unique_id} - {self.student.name}"

    def generate_reg_unique_id(self):
        today = timezone.now()
        date_str = today.strftime("%m%y")
        library_name = self.student.librarian.library_name.replace(" ", "_")  # type: ignore

        # Initialize count
        count = 1

        while True:
            reg_unique_id = f"reg_{library_name}_{date_str}_{count:03d}"
            if not RegistrationFee.objects.filter(reg_unique_id=reg_unique_id).exists():
                return reg_unique_id
            count += 1

    def save(self, *args, **kwargs):
        if not self.reg_unique_id:
            self.reg_unique_id = self.generate_reg_unique_id()

        if not self.slug:
            self.slug = slugify(self.reg_unique_id)

        super().save(*args, **kwargs)


class TempStudentData(models.Model):
    librarian = models.ForeignKey(
        Librarian_param, on_delete=models.CASCADE, null=True, blank=True
    )
    course = models.ForeignKey(Courses, on_delete=models.CASCADE)
    name = models.CharField(max_length=150)
    f_name = models.CharField(max_length=100, null=True, blank=True, default=None)
    age = models.IntegerField(null=True, blank=True, default=0)
    gender = models.CharField(max_length=100)
    email = models.EmailField(max_length=100)
    mobile = models.BigIntegerField()
    locality = models.CharField(max_length=100)
    city = models.CharField(max_length=100)
    state = models.ForeignKey(States, on_delete=models.CASCADE)
    registration_date = models.DateField(default=timezone.now)
    image = models.ImageField(upload_to="temp_student_image/", blank=True, null=True)
    status = models.CharField(max_length=20, default="pending")
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.librarian} - {self.name}"


# Seat model definition
class Seat(models.Model):
    librarian = models.ForeignKey(Librarian_param, on_delete=models.CASCADE)
    shift = models.ForeignKey(Shift, related_name="seats", on_delete=models.CASCADE)
    seat_number = models.CharField(max_length=20)
    is_available = models.BooleanField(default=True)

    class Meta:
        unique_together = (
            "librarian",
            "seat_number",
            "shift",
        )

    def __str__(self):
        return f"{self.librarian}-{self.seat_number} - {self.shift}"


# Booking model that references Shift model from the librarian app
class Booking(models.Model):
    student = models.ForeignKey(StudentData, on_delete=models.CASCADE)
    seat = models.ForeignKey(Seat, on_delete=models.CASCADE, related_name="booking_set")
    booking_date = models.DateField(auto_now_add=True)
    expire_date = models.DateField(blank=True, null=True)

    class Meta:
        unique_together = (
            "student",
            "seat",
        )

    def __str__(self):
        return f"{self.seat} - {self.booking_date}"


class ShortenedURL(models.Model):
    original_url = models.URLField()
    short_code = models.CharField(max_length=10, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        if not self.short_code:
            self.short_code = self.generate_short_code()
        super().save(*args, **kwargs)

    def generate_short_code(self):
        return "".join(random.choices(string.ascii_letters + string.digits, k=6))

    def get_short_url(self, request):
        return request.build_absolute_uri(f"/students/s/{self.short_code}/")

