<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="google" content="notranslate">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Update Temporary Student - Librainian</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-wEmeIV1mKuiNpC+IOBjI7aAzPcEZeedi5yW5f2yOq55WWLwNGmvvx4Um1vskeMj0" crossorigin="anonymous">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0/dist/js/bootstrap.bundle.min.js" integrity="sha384-p34f1UUtsS3wqzfto5wAAmdvj+osOnFyQFpp4Ua3gs/ZVWx6oOypYoCJhGGScy+8" crossorigin="anonymous"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
  <!-- Favicon -->
  <link href="/static/img/librainian-logo-black-transparent.png" rel="icon">
  <script>
    // Inline loader script to show loader immediately when page starts loading
    (function() {
        // Create loader HTML with FSEX300 font and LIBRAINIAN text
        var loaderHTML = `
        <div id="initialLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.85); z-index: 9999;">
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                <style>
                    @font-face {
                        font-family: 'FSEX300';
                        src: url('/static/fonts/FSEX300.ttf') format('truetype');
                        font-weight: normal;
                        font-style: normal;
                    }

                    @keyframes blink {
                        0%, 100% { opacity: 1; }
                        50% { opacity: 0; }
                    }

                    @keyframes dots {
                        0% { content: ""; }
                        25% { content: "."; }
                        50% { content: ".."; }
                        75% { content: "..."; }
                        100% { content: ""; }
                    }

                    .loader-text {
                        font-family: 'FSEX300', monospace;
                        font-size: 32px;
                        color: #ffffff;
                        letter-spacing: 2px;
                        margin-bottom: 20px;
                        text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                    }

                    .loader-dots::after {
                        content: "";
                        animation: dots 1.5s infinite;
                    }

                    .loader-bar {
                        width: 300px;
                        height: 20px;
                        background-color: rgba(255, 255, 255, 0.2);
                        border-radius: 10px;
                        overflow: hidden;
                        margin: 20px auto;
                    }

                    .loader-progress {
                        width: 0%;
                        height: 100%;
                        background-color: #6200ee;
                        border-radius: 10px;
                        animation: progress 2s infinite;
                    }

                    @keyframes progress {
                        0% { width: 0%; }
                        50% { width: 100%; }
                        100% { width: 0%; }
                    }
                </style>
                <div class="loader-text">LIBRAINIAN<span class="loader-dots"></span></div>
                <div class="loader-bar">
                    <div class="loader-progress"></div>
                </div>
            </div>
        </div>
        `;

        // Add loader to page
        document.write(loaderHTML);

        // Remove loader when page is loaded
        window.addEventListener('load', function() {
            var loader = document.getElementById('initialLoader');
            if (loader) {
                loader.style.display = 'none';
            }
        });
    })();
</script>

<style>
    :root {
        --primary-color: #042299;
        --primary-hover: #031b77;
        --secondary-color: #6200ee;
        --secondary-hover: #3700b3;
        --background-color: #cee3e0;
        --surface-color: #ffffff;
        --text-primary: #212121;
        --text-secondary: #757575;
        --border-color: #e0e0e0;
        --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        --shadow-hover: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    body {
        font-family: 'Comfortaa', sans-serif !important;
        background: var(--background-color) !important;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        margin: 0.5rem;
    }

    body h1, h2, h3, h4, h5, h6, p, ul, li, strong, em, b, s, small, span {
        font-family: 'Comfortaa', sans-serif !important;
    }

    .card {
        border-radius: 1rem;
        box-shadow: var(--shadow);
        border: none;
        background: var(--surface-color);
    }

    .btn {
        border-radius: 1rem !important;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 1px;
        padding: 12px 24px;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        box-shadow: var(--shadow);
    }

    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    .btn-primary:hover {
        background-color: var(--primary-hover);
        border-color: var(--primary-hover);
        transform: translateY(-2px);
        box-shadow: var(--shadow-hover);
        color: white;
    }

    .btn-secondary {
        background-color: var(--text-secondary);
        border-color: var(--text-secondary);
        color: white;
    }

    .btn-secondary:hover {
        background-color: var(--text-primary);
        border-color: var(--text-primary);
        transform: translateY(-2px);
        box-shadow: var(--shadow-hover);
        color: white;
    }

    .form-control, .form-select {
        padding: 0.75rem 1rem;
        border-radius: 1rem !important;
        border: 2px solid var(--border-color);
        transition: all 0.3s ease;
        font-family: 'Comfortaa', sans-serif;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(4, 34, 153, 0.25);
    }

    .form-label {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
    }

    h2 {
        font-weight: 700;
        color: var(--primary-color);
    }

    /* Mobile Responsive Design */
    @media only screen and (max-width: 767px) {
        .d-sm-none {
            display: none !important;
        }

        .btn-mobile-full {
            width: 100% !important;
            margin-bottom: 0.5rem;
        }

        .mobile-stack {
            flex-direction: column !important;
        }

        .card {
            margin: 0.5rem;
        }

        body {
            margin: 0.25rem;
        }
    }

    /* Desktop specific styles */
    @media only screen and (min-width: 768px) {
        .btn-desktop {
            min-width: 120px;
        }
    }
</style>

</head>
<body>
  {% block content %}
  <div class="container mt-4">
    <div class="row justify-content-center">
      <div class="col-12 col-md-8 col-lg-6">
        <div class="text-center mb-4">
          <h2 class="mb-2">
            <i class="fas fa-user-edit me-2"></i>Update Temporary Student
          </h2>
          <p class="text-muted">Modify student information and status</p>
        </div>

        <div class="card shadow-lg p-4">
      <form method="POST">
        {% csrf_token %}
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="name" class="form-label">
              <i class="fas fa-user me-1"></i>Name
            </label>
            <input type="text" id="name" name="name" class="form-control" value="{{ temp_student.name }}" placeholder="Enter student name" required>
          </div>
          <div class="col-md-6">
            <label for="f_name" class="form-label">
              <i class="fas fa-male me-1"></i>Father's Name
            </label>
            <input type="text" id="f_name" name="f_name" class="form-control" value="{{ temp_student.f_name }}" placeholder="Enter father's name" >
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-md-6">
            <label for="age" class="form-label">
              <i class="fas fa-birthday-cake me-1"></i>Age
            </label>
            <input type="number" id="age" name="age" class="form-control" value="{{ temp_student.age }}" placeholder="Enter age" min="10" max="100">
          </div>
          <div class="col-md-6">
            <label for="gender" class="form-label">
              <i class="fas fa-venus-mars me-1"></i>Gender
            </label>
            <select id="gender" name="gender" class="form-select" required>
              <option value="">Select Gender</option>
              <option value="Male" {% if temp_student.gender == "male" %}selected{% endif %}>Male</option>
              <option value="Female" {% if temp_student.gender == "female" %}selected{% endif %}>Female</option>
              <option value="Other" {% if temp_student.gender == "other" %}selected{% endif %}>Other</option>
            </select>
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-md-6">
            <label for="email" class="form-label">
              <i class="fas fa-envelope me-1"></i>Email
            </label>
            <input type="email" id="email" name="email" class="form-control" value="{{ temp_student.email }}" placeholder="Enter email address" required>
          </div>
          <div class="col-md-6">
            <label for="mobile" class="form-label">
              <i class="fas fa-phone me-1"></i>Mobile
            </label>
            <input type="tel" id="mobile" name="mobile" class="form-control" value="{{ temp_student.mobile }}" placeholder="Enter mobile number" required>
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-md-6">
            <label for="locality" class="form-label">
              <i class="fas fa-map-marker-alt me-1"></i>Locality
            </label>
            <input type="text" id="locality" name="locality" class="form-control" value="{{ temp_student.locality }}" placeholder="Enter locality">
          </div>
          <div class="col-md-6">
            <label for="city" class="form-label">
              <i class="fas fa-city me-1"></i>City
            </label>
            <input type="text" id="city" name="city" class="form-control" value="{{ temp_student.city }}" placeholder="Enter city" required>
          </div>
        </div>

        <div class="row mb-3">
          <div class="col-md-6">
            <label for="state" class="form-label">
              <i class="fas fa-map me-1"></i>State
            </label>
            <input type="text" id="state" name="state" class="form-control" value="{{ temp_student.state }}" placeholder="Enter state" required>
          </div>
        </div>

        <div class="mb-4">
          <label for="status" class="form-label">
            <i class="fas fa-flag me-1"></i>Status
          </label>
          <select id="status" name="status" class="form-select" required>
            <option value="">Select Status</option>
            <option value="pending" {% if temp_student.status == "pending" %}selected{% endif %}>
              <i class="fas fa-clock"></i> Pending
            </option>
            <option value="completed" {% if temp_student.status == "completed" %}selected{% endif %}>
              <i class="fas fa-check"></i> Completed
            </option>
          </select>
        </div>

        <div class="d-flex justify-content-between mobile-stack">
          <button type="submit" class="btn btn-primary btn-mobile-full btn-desktop">
            <i class="fas fa-save me-2"></i>Update Student
          </button>
          <a href="{% url 'temp_students_list' %}" class="btn btn-secondary btn-mobile-full btn-desktop">
            <i class="fas fa-arrow-left me-2"></i>Back to List
          </a>
        </div>
      </form>
        </div>
      </div>
    </div>
  </div>
  {% endblock %}
</body>
</html>
