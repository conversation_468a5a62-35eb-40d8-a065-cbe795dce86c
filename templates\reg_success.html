<!DOCTYPE html>
<html lang="en">
<head>
    <script>
        // Inline loader script to show loader immediately when page starts loading
        (function() {
            // Create loader HTML with FSEX300 font and LIBRAINIAN text
            var loaderHTML = `
            <div id="initialLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.85); z-index: 9999;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                    <style>
                        @font-face {
                            font-family: 'FSEX300';
                            src: url('/static/fonts/FSEX300.ttf') format('truetype');
                            font-weight: normal;
                            font-style: normal;
                        }

                        @keyframes blink {
                            0%, 100% { opacity: 1; }
                            50% { opacity: 0; }
                        }

                        @keyframes dots {
                            0% { content: ""; }
                            25% { content: "."; }
                            50% { content: ".."; }
                            75% { content: "..."; }
                            100% { content: ""; }
                        }

                        .loader-text {
                            font-family: 'FSEX300', monospace;
                            font-size: 32px;
                            color: #ffffff;
                            letter-spacing: 2px;
                            margin-bottom: 20px;
                            text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                        }

                        .loader-dots::after {
                            content: "";
                            animation: dots 1.5s infinite;
                        }

                        .loader-bar {
                            width: 300px;
                            height: 20px;
                            background-color: rgba(255, 255, 255, 0.2);
                            border-radius: 10px;
                            overflow: hidden;
                            margin: 20px auto;
                        }

                        .loader-progress {
                            width: 0%;
                            height: 100%;
                            background-color: #6200ee;
                            border-radius: 10px;
                            animation: progress 2s infinite;
                        }

                        @keyframes progress {
                            0% { width: 0%; }
                            50% { width: 100%; }
                            100% { width: 0%; }
                        }
                    </style>
                    <div class="loader-text">LIBRAINIAN<span class="loader-dots"></span></div>
                    <div class="loader-bar">
                        <div class="loader-progress"></div>
                    </div>
                </div>
            </div>
            `;

            // Add loader to page
            document.write(loaderHTML);

            // Remove loader when page is loaded
            window.addEventListener('load', function() {
                var loader = document.getElementById('initialLoader');
                if (loader) {
                    loader.style.display = 'none';
                }
            });
        })();
    </script>




    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Success</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts - Comfortaa font as requested -->
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Global Loader Script -->
     
    <!-- Custom styles -->
    <style>
        /* Fix for asterisks and other inline elements */
        span {
            display: inline !important;
        }

        body {
            background: linear-gradient(135deg, #673ab7, #512da8) fixed; /* Material purple gradient - fixed to prevent scrolling issues */
            font-family: 'Comfortaa', cursive;
            padding-top: 30px;
            padding-bottom: 80px; /* Space for sticky button */
            min-height: 100vh; /* Ensure the gradient covers the entire viewport height */
        }
        .success-card {
            background-color: white;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 30px 20px;
            max-width: 800px;
            margin: 0 auto 30px;
            width: 95%;
        }
        .success-icon {
            font-size: 70px;
            color: #28a745;
            margin-bottom: 15px;
        }
        .success-title {
            color: #28a745;
            font-weight: 600;
            margin-bottom: 20px;
            font-size: calc(1.3rem + 1vw);
            word-wrap: break-word;
        }
        .feedback-section {
            background-color: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .home-button {
            background-color: #6200ee;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s;
            box-shadow: 0 4px 8px rgba(98, 0, 238, 0.3);
        }
        .home-button:hover {
            background-color: #3700b3;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(98, 0, 238, 0.4);
        }
        .close-info {
            margin-top: 25px;
            color: #ffffff;
            font-size: 14px;
        }
        .sticky-bottom {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: rgba(255, 255, 255, 0.95);
            padding: 15px;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }
        .feedback-form {
            background-color: #ffffff;
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        .form-control {
            border-radius: 12px;
            padding: 12px;
            font-family: 'Comfortaa', cursive;
            border: 1px solid #ddd;
            transition: all 0.3s;
        }
        .form-control:focus {
            border-color: #6200ee;
            box-shadow: 0 0 0 0.2rem rgba(98, 0, 238, 0.25);
        }
        .form-label {
            font-size: 16px;
            color: #34495e;
            font-weight: 500;
            margin-bottom: 8px;
        }
        .btn-submit {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 30px;
            font-weight: 600;
            transition: all 0.3s;
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
        }
        .btn-submit:hover {
            background-color: #218838;
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(40, 167, 69, 0.4);
        }
        .form-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .registration-details {
            background-color: #f0f8ff;
            border-radius: 12px;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .registration-details h4 {
            color: #007bff;
            font-size: 1.2rem;
            margin-bottom: 10px;
        }
        .registration-details p {
            margin-bottom: 5px;
            font-size: 1rem;
        }
        .registration-details strong {
            font-weight: 600;
            color: #343a40;
        }

        /* Media queries for better responsiveness */
        @media (max-width: 576px) {
            body {
                padding-top: 15px;
            }
            .success-card {
                padding: 20px 15px;
                width: 92%;
                border-radius: 12px;
            }
            .success-icon {
                font-size: 60px;
            }
            .success-title {
                font-size: 1.5rem;
            }
            .feedback-section {
                padding: 15px;
            }
            .home-button {
                padding: 15px 20px;
                font-size: 0.9rem;
            }
            p {
                font-size: 0.75rem;
            }
            .feedback-form {
                padding: 20px 15px;
            }
            .form-control {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container px-0">
        <div class="success-card text-center">
            <div class="material-icons success-icon">check_circle</div>
            <h1 class="success-title">{% if student.name %}{{ student.name }}{% else %}Thank you{% endif %}, Registration Form Submitted.</h1>
            <p class="lead">Thank you for registering via QR Code. <br> You can Exit or scroll down to help us improve. <br> {% if student.unique_id %}Your registration number: <strong>{{ student.unique_id }}</strong>{% endif %}</p>

            <!-- Feedback Form Section -->
            <div class="row justify-content-center mt-4">
                <div class="col-md-10">
                    <div class="feedback-form" id="feedbackFormContainer">
                        <!-- Thank you message -->
                        {% if feedback_submitted %}
                        <div id="thankYouMessage" class="text-center py-4">
                            <div class="material-icons" style="font-size: 60px; color: #28a745; margin-bottom: 20px;">thumb_up</div>
                            <h4 style="color: #28a745; margin-bottom: 15px;">Thank You for Your Feedback!</h4>
                            <p>Your input is valuable to us and will help us improve our services.</p>
                        </div>
                        {% else %}

                        <div id="feedbackFormContent">
                            <h4 class="form-title text-center">Help Us</h4>
                            <p class="text-center mb-4">We are working really hard, your response will help us improve and keep us motivated.</p>

                            <form method="post" action="{% url 'reg_feedback' %}" class="needs-validation" novalidate>
                                {% csrf_token %}

                                <!-- Hidden fields to store student information -->
                                {% if student %}
                                <input type="hidden" name="name" value="{{ student.name }}">
                                <input type="hidden" name="email" value="{{ student.email }}">
                                <input type="hidden" name="phone" value="{{ student.mobile }}">
                                {% if student.librarian %}
                                <input type="hidden" name="library" value="{{ student.librarian.id }}">
                                {% endif %}
                                {% endif %}

                                <input type="hidden" name="subject" value="Registration Feedback">

                                <div class="mb-3">
                                    <label class="form-label">How fast was the form submission process?</label>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Very Slow</span>
                                        <span>Very Fast</span>
                                    </div>
                                    <input type="range" class="form-range" min="1" max="5" id="submissionSpeed" name="submissionSpeed" value="3">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Did you get all the information in the correct manner?</label>
                                    <select class="form-select" id="informationCorrectness" name="informationCorrectness" required>
                                        <option value="" selected disabled>Please select an option</option>
                                        <option value="yes">Yes, everything was clear</option>
                                        <option value="mostly">Mostly, with minor issues</option>
                                        <option value="partially">Partially, some information was unclear</option>
                                        <option value="no">No, the information was confusing</option>
                                    </select>
                                    <div class="invalid-feedback">Please select an option.</div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">How was the look and feel of the application?</label>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Poor</span>
                                        <span>Excellent</span>
                                    </div>
                                    <input type="range" class="form-range" min="1" max="5" id="lookAndFeel" name="lookAndFeel" value="3">
                                </div>

                                <div class="mb-4">
                                    <label for="message" class="form-label">Additional Feedback</label>
                                    <textarea class="form-control" id="message" name="message" rows="3" placeholder="Please share any additional thoughts or suggestions..."></textarea>
                                </div>

                                <!-- Hidden field for rating -->
                                <input type="hidden" name="rating" id="rating" value="5">

                                <div class="text-center">
                                    <button type="submit" class="btn btn-submit px-4 py-2">Submit Feedback</button>
                                </div>
                            </form>
                        </div> <!-- Close feedbackFormContent -->
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sticky Home Button at the bottom -->
    <div class="sticky-bottom">
        <div class="container text-center">
            <a href="/" class="btn home-button">
                <span class="material-icons align-middle me-2">home</span>
                Explore the App Details
            </a>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Form Validation Script -->
    <script>
    // Form validation
    (function() {
        'use strict';

        // Fetch all forms to which we want to apply validation
        var forms = document.querySelectorAll('.needs-validation');

        // Loop over them and prevent submission
        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                    form.classList.add('was-validated');
                } else {
                    // Set the rating based on lookAndFeel value
                    document.getElementById('rating').value = document.getElementById('lookAndFeel').value;

                    // Continue with form submission
                    // The server will handle the redirect
                }
            }, false);
        });

        // Update rating value when lookAndFeel slider changes
        document.getElementById('lookAndFeel').addEventListener('input', function() {
            document.getElementById('rating').value = this.value;
        });
    })();
    </script>
</body>
</html>
