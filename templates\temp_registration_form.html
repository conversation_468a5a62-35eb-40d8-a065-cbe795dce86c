<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Inline Loader Script - Shows loader immediately when page starts loading -->
    <script>
        // Inline loader script to show loader immediately when page starts loading
        (function() {
            // Create loader HTML with FSEX300 font and LIBRAINIAN text
            var loaderHTML = `
            <div id="initialLoader" style="display: block; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.85); z-index: 9999;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                    <style>
                        @font-face {
                            font-family: 'FSEX300';
                            src: url('  /static/fonts/FSEX300.ttf') format('truetype');
                            font-weight: normal;
                            font-style: normal;
                        }

                        @keyframes blink {
                            0%, 100% { opacity: 1; }
                            50% { opacity: 0; }
                        }

                        @keyframes dots {
                            0% { content: ""; }
                            25% { content: "."; }
                            50% { content: ".."; }
                            75% { content: "..."; }
                            100% { content: ""; }
                        }

                        .loader-text {
                            font-family: 'FSEX300', monospace;
                            font-size: 32px;
                            color: #ffffff;
                            letter-spacing: 2px;
                            margin-bottom: 20px;
                            text-shadow: 0 0 10px rgba(98, 0, 238, 0.8);
                        }

                        .loader-dots::after {
                            content: "";
                            animation: dots 1.5s infinite;
                        }

                        .loader-bar {
                            width: 300px;
                            height: 20px;
                            background-color: rgba(255, 255, 255, 0.2);
                            border-radius: 10px;
                            overflow: hidden;
                            margin: 20px auto;
                        }

                        .loader-progress {
                            width: 0%;
                            height: 100%;
                            background-color: #6200ee;
                            border-radius: 10px;
                            animation: progress 2s infinite;
                        }

                        @keyframes progress {
                            0% { width: 0%; }
                            50% { width: 100%; }
                            100% { width: 0%; }
                        }
                    </style>
                    <div class="loader-text">LIBRAINIAN<span class="loader-dots"></span></div>
                    <div class="loader-bar">
                        <div class="loader-progress"></div>
                    </div>
                </div>
            </div>
            `;

            // Add loader to page
            document.write(loaderHTML);

            // Remove loader when page is loaded
            window.addEventListener('load', function() {
                var loader = document.getElementById('initialLoader');
                if (loader) {
                    loader.style.display = 'none';
                }
            });
        })();
    </script>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration via QR</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts - Roboto for Material Design -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Google Fonts - Inter -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600&display=swap" rel="stylesheet">
    <!-- Material Design for Bootstrap CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.0/mdb.min.css" rel="stylesheet">
    <!-- Global Loader Script -->
     
 
  

        <!-- disable Print Screen for Windows -->

          

        <!-- disable print screen for mac -->

          

        <!-- disabling print screen for Linux -->

          

        <!-- disabling inspection tool -->

          


   <style>
        :root {
            --primary-color: #6200ee; /* Material primary purple */
            --primary-light: #bb86fc;
            --primary-dark: #3700b3;
            --secondary-color: #03dac6; /* Material secondary teal */
            --secondary-dark: #018786;
            --surface-color: #ffffff;
            --background-color: #f5f5f5;
            --error-color: #b00020;
            --text-primary: #212121;
            --text-secondary: #757575;
            --border-color: #e0e0e0;
        }

        /* Fix for asterisks and other inline elements */
        span {
            display: inline !important;
        }


        body {
            -webkit-user-select: none; /* Disable text selection */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            font-family: 'Roboto', sans-serif;
            color: var(--text-primary);
            line-height: 1.6;
            background: linear-gradient(135deg, #673ab7, #512da8) fixed; /* Material purple gradient - fixed to prevent scrolling issues */
            margin: 0;
            padding: 0;
            min-height: 100vh; /* Ensure the gradient covers the entire viewport height */
        }

        .registration-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 30px 0;
        }

        .registration-container {
            background-color: var(--surface-color);
            border-radius: 16px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.12);
            padding: 40px;
            max-width: 800px;
            width: 90%;
            position: relative;
            overflow: hidden;
        }

        /* Material Design accent bar */
        .registration-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: var(--primary-color);
        }

        .registration-container h2 {
            color: var(--text-primary);
            margin-bottom: 24px;
            text-align: center;
            font-weight: 500;
            font-size: 1.75rem;
        }

        /* Form header with icon */
        .form-header {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 32px;
        }

        .form-header .material-icons {
            font-size: 36px;
            color: var(--primary-color);
            margin-right: 12px;
        }

        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        .form-label {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 8px;
            display: block;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        /* Material Design form fields */
        .form-control,
        .form-select {
            padding: 12px 16px;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            font-size: 1rem;
            width: 100%;
            transition: all 0.3s ease;
            background-color: rgba(255, 255, 255, 0.9);
        }

        .form-control:focus,
        .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 1px var(--primary-light);
            outline: none;
        }

        /* Input with icon */
        .input-with-icon {
            position: relative;
        }

        .input-with-icon .material-icons {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 20px;
        }

        .input-with-icon .form-control,
        .input-with-icon .form-select {
            padding-left: 40px;
        }

        .input-focused .material-icons {
            color: var(--primary-color) !important;
        }

        /* Material Design button */
        .btn-primary {
            background-color: var(--primary-color);
            border: none;
            padding: 12px 24px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1.25px;
            border-radius: 4px;
            box-shadow: 0 3px 5px rgba(0,0,0,0.2);
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }

        .btn-primary:hover, .btn-primary:focus {
            background-color: var(--primary-dark);
            box-shadow: 0 6px 10px rgba(0,0,0,0.25);
            transform: translateY(-2px);
        }

        .btn-primary:active {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(0,0,0,0.15);
        }

        /* File input styling */
        .file-input-container {
            position: relative;
            overflow: hidden;
            display: inline-block;
            width: 100%;
        }

        .file-input-label {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background-color: #f5f5f5;
            border: 1px dashed var(--border-color);
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-input-label:hover {
            background-color: #eeeeee;
            border-color: var(--primary-light);
        }

        .file-input-label .material-icons {
            margin-right: 8px;
            color: var(--primary-color);
        }

        .file-input-text {
            flex-grow: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Helper text and error messages */
        .text-danger {
            color: var(--error-color) !important;
            font-size: 0.8rem;
            margin-top: 4px;
            display: block;
        }

        .form-text {
            color: var(--text-secondary);
            font-size: 0.8rem;
            margin-top: 4px;
        }

        /* Alert styling */
        .alert {
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            border: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .alert-success {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .registration-container {
                padding: 24px;
                width: 95%;
                margin: 16px auto;
            }

            .form-group {
                margin-bottom: 20px;
            }

            .btn-primary {
                width: 100%;
                padding: 12px 16px;
            }
        }

        /* Animation for form elements */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .form-group {
            animation: fadeIn 0.5s ease forwards;
        }

        .form-group:nth-child(1) { animation-delay: 0.1s; }
        .form-group:nth-child(2) { animation-delay: 0.2s; }
        .form-group:nth-child(3) { animation-delay: 0.3s; }
        .form-group:nth-child(4) { animation-delay: 0.4s; }
        .form-group:nth-child(5) { animation-delay: 0.5s; }
        .form-group:nth-child(6) { animation-delay: 0.6s; }

        /* Ripple effect for buttons */
        .btn-primary {
            position: relative;
            overflow: hidden;
        }

        .btn-primary:after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }

        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }
            100% {
                transform: scale(20, 20);
                opacity: 0;
            }
        }

        .btn-primary:focus:not(:active)::after {
            animation: ripple 1s ease-out;
        }
    </style>
</head>
<body>


    <div class="registration-wrapper">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-12 col-md-10 col-lg-8">
                {% if messages %}
                    <div id="messages">
                        {% for message in messages %}
                            <div class="alert alert-success" role="alert">
                                {{ message }}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
                    <div class="registration-container">
                        <div class="form-header">
                            <span class="material-icons">how_to_reg</span>
                            <h2 class="mb-0">{{librarian.library_name}} Registration</h2>
                        </div>
                        <p class="mb-3 text-center">Please fill in the following details to complete your registration.</p>
                        <p class="mb-4 text-center">Fields marked with <span class="text-danger" style="display: inline;">*</span> are mandatory.</p>
                        <div class="mb-4" style="height: 1px; background: linear-gradient(to right, transparent, var(--border-color), transparent);"></div>
                        <form method="POST" enctype="multipart/form-data">
                            {% csrf_token %}

                            <div class="row">
                                <div class="col-md-6 form-group">
                                    <label for="name" class="form-label">Full Name <span class="text-danger" style="display: inline;">*</span></label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">person</span>
                                        <input type="text" class="form-control" id="name" name="name" required maxlength="150" placeholder="Enter full name">
                                    </div>
                                </div>
                                <div class="col-md-6 form-group">
                                    <label for="f_name" class="form-label">Father's Name</label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">people</span>
                                        <input type="text" class="form-control" id="f_name" name="f_name" maxlength="100" placeholder="Enter father's name">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 form-group">
                                    <label for="age" class="form-label">Age</label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">cake</span>
                                        <input type="number" class="form-control" id="age" name="age" min="0" max="150" placeholder="Enter age">
                                    </div>
                                </div>
                                <div class="col-md-6 form-group">
                                    <label for="gender" class="form-label">Gender <span class="text-danger" style="display: inline;">*</span></label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">wc</span>
                                        <select class="form-select" id="gender" name="gender" required>
                                            <option value="">Select Gender</option>
                                            <option value="male">Male</option>
                                            <option value="female">Female</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 form-group">
                                    <label for="email" class="form-label">Email <span class="text-danger" style="display: inline;">*</span></label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">email</span>
                                        <input type="email" class="form-control" id="email" name="email" required maxlength="100" placeholder="Enter email address">
                                    </div>
                                </div>
                                <div class="col-md-6 form-group">
                                    <label for="mobile" class="form-label">Mobile Number <span class="text-danger" style="display: inline;">*</span></label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">phone</span>
                                        <input type="tel" class="form-control" id="mobile" name="mobile" required pattern="[0-9]{10}" placeholder="Enter 10-digit mobile number">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 form-group">
                                    <label for="locality" class="form-label">Locality <span class="text-danger" style="display: inline;">*</span></label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">location_on</span>
                                        <input type="text" class="form-control" id="locality" name="locality" required maxlength="100" placeholder="Enter locality">
                                    </div>
                                </div>
                                <div class="col-md-6 form-group">
                                    <label for="city" class="form-label">City <span class="text-danger" style="display: inline;">*</span></label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">location_city</span>
                                        <input type="text" class="form-control" id="city" name="city" required maxlength="100" placeholder="Enter city name" required pattern="[A-Za-z ]+" title="Please enter only alphabets">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 form-group">
                                    <label for="state" class="form-label">State <span class="text-danger" style="display: inline;">*</span></label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">map</span>
                                        <select class="form-select" id="states" name="state" required>
                                            <option value="">Select a state</option>
                                            {% for state in states %}
                                            <option value="{{ state.id }}">{{ state.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6 form-group">
                                    <label for="course" class="form-label">Course <span class="text-danger" style="display: inline;">*</span></label>
                                    <div class="input-with-icon">
                                        <span class="material-icons">school</span>
                                        <select class="form-select" id="courseName" name="course" required>
                                            <option value="">Select a course</option>
                                            {% for course in courses %}
                                            <option value="{{ course.id }}">{{ course.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group mb-4">
                                <label for="image" class="form-label">Student Image</label>
                                <div class="file-input-container">
                                    <label for="image" class="file-input-label">
                                        <span class="material-icons">add_photo_alternate</span>
                                        <span class="file-input-text">Choose a file...</span>
                                    </label>
                                    <input type="file" class="form-control d-none" id="image" name="image" accept="image/*">
                                </div>
                                <small class="form-text text-muted">Upload a clear passport-size photograph (Max 5MB)</small>
                            </div>

                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <span class="material-icons align-middle me-2">check_circle</span>
                                    Register Now
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Material Design for Bootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mdb-ui-kit/6.4.0/mdb.min.js"></script>

    <!-- Loader Script already included in head -->

    <!-- Form Validation and Enhancement Script -->
    <script>
        (function() {
            'use strict';

            // File input enhancement
            const fileInput = document.getElementById('image');
            const fileInputText = document.querySelector('.file-input-text');

            if (fileInput && fileInputText) {
                fileInput.addEventListener('change', function() {
                    if (fileInput.files.length > 0) {
                        fileInputText.textContent = fileInput.files[0].name;
                    } else {
                        fileInputText.textContent = 'Choose a file...';
                    }
                });
            }

            // Form validation
            window.addEventListener('load', function() {
                var form = document.querySelector('form');
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();

                        // Find the first invalid input and focus it
                        const invalidInputs = form.querySelectorAll(':invalid');
                        if (invalidInputs.length > 0) {
                            invalidInputs[0].focus();

                            // Scroll to the invalid input
                            invalidInputs[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }
                    }
                    form.classList.add('was-validated');
                }, false);
            }, false);

            // Add ripple effect to buttons
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    const rect = button.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    const ripple = document.createElement('span');
                    ripple.style.position = 'absolute';
                    ripple.style.width = '1px';
                    ripple.style.height = '1px';
                    ripple.style.borderRadius = '50%';
                    ripple.style.backgroundColor = 'rgba(255, 255, 255, 0.7)';
                    ripple.style.transform = 'scale(0)';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.style.animation = 'ripple 0.6s linear';
                    ripple.style.pointerEvents = 'none';

                    button.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Add focus effects to inputs
            const inputs = document.querySelectorAll('.form-control, .form-select');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('input-focused');
                });

                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('input-focused');
                });
            });
        })();
    </script>
</body>
</html>